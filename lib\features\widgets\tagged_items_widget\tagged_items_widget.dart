import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class TaggedItemsWidget extends StatefulWidget {
  final List<SuggestionItem> taggedItems;
  final Function(int)? onRemoveItem; // Optional for post cards (no remove functionality)
  final bool showRemoveButton;
  final String selectedTab;
  final Function(String)? onTabChanged;
  final bool showTabs;
  final bool showFullPreviewByDefault; // New parameter to control default behavior
  final Function(String)? onTabTapped; // Callback for when tab is tapped (for API calls)

  const TaggedItemsWidget({
    super.key,
    required this.taggedItems,
    this.onRemoveItem,
    this.showRemoveButton = false,
    this.selectedTab = 'PRODUCT',
    this.onTabChanged,
    this.showTabs = true,
    this.showFullPreviewByDefault = false, // Default to collapsed (for post cards)
    this.onTabTapped, // Optional callback for API calls
  });

  @override
  State<TaggedItemsWidget> createState() => _TaggedItemsWidgetState();
}

class _TaggedItemsWidgetState extends State<TaggedItemsWidget> {
  late String currentSelectedTab;

  @override
  void initState() {
    super.initState();

    if (widget.showFullPreviewByDefault) {
      // For add/edit post screens: automatically select the first available tab
      if (widget.selectedTab.isNotEmpty &&
          widget.taggedItems.any((item) => item.type == widget.selectedTab)) {
        currentSelectedTab = widget.selectedTab;
      } else {
        // Find the first available tab type
        if (widget.taggedItems.any((item) => item.type == 'PRODUCT')) {
          currentSelectedTab = 'PRODUCT';
        } else if (widget.taggedItems.any((item) => item.type == 'STORE')) {
          currentSelectedTab = 'STORE';
        } else if (widget.taggedItems.any((item) => item.type == 'USER')) {
          currentSelectedTab = 'USER';
        } else {
          currentSelectedTab = '';
        }
      }
    } else {
      // For post cards: start with no tab selected (collapsed view)
      currentSelectedTab = '';
    }
  }

  @override
  void didUpdateWidget(TaggedItemsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the selectedTab prop changes, update the current selection
    if (widget.selectedTab != oldWidget.selectedTab) {
      if (widget.selectedTab.isNotEmpty &&
          widget.taggedItems.any((item) => item.type == widget.selectedTab)) {
        setState(() {
          currentSelectedTab = widget.selectedTab;
        });
      } else if (currentSelectedTab.isNotEmpty && 
                 !widget.taggedItems.any((item) => item.type == currentSelectedTab)) {
        // If the current selected tab no longer has items, deselect it
        setState(() {
          currentSelectedTab = '';
        });
      }
    }
  }

  List<SuggestionItem> getFilteredItems() {
    return widget.taggedItems
        .where((item) => item.type == currentSelectedTab)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    // Only show the tab row if there are tabs to show
    final tabRow = widget.showTabs ? _buildTabRow() : const SizedBox.shrink();
    final hasTabs = tabRow is! SizedBox || tabRow.child != null;
    
    // Only show the items list if a tab is selected and it has items
    final itemsList = currentSelectedTab.isNotEmpty ? _buildTaggedItemsList() : const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (hasTabs) tabRow,
        if (hasTabs) const SizedBox(height: 12),
        itemsList,
      ],
    );
  }

  Widget _buildTabRow() {
    // Get counts for each tab type
    final productCount = widget.taggedItems.where((item) => item.type == 'PRODUCT').length;
    final storeCount = widget.taggedItems.where((item) => item.type == 'STORE').length;
    final userCount = widget.taggedItems.where((item) => item.type == 'USER').length;

    // Create a list of tabs to show
    final tabs = <Widget>[];

    // Add Product tab if there are products
    if (productCount > 0) {
      tabs.add(_buildTabItem('PRODUCT', 'Products', 
          currentSelectedTab == 'PRODUCT' ? AppImages.tabProductActive : AppImages.tabProductInactive));
      tabs.add(const SizedBox(width: 12));
    }

    // Add Store tab if there are stores
    if (storeCount > 0) {
      tabs.add(_buildTabItem('STORE', 'Stores', 
          currentSelectedTab == 'STORE' ? AppImages.createStoreIcon : AppImages.createStoreIcon));
      tabs.add(const SizedBox(width: 12));
    }

    // Add User tab if there are users
    if (userCount > 0) {
      tabs.add(_buildTabItem('USER', 'Users', 
          currentSelectedTab == 'USER' ? AppImages.userIcon : AppImages.userIcon));
      tabs.add(const SizedBox(width: 12));
    }

    // Remove the last SizedBox if there are tabs
    if (tabs.isNotEmpty) {
      tabs.removeLast();
    }

    // If no tabs to show, return empty container
    if (tabs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(children: tabs),
      ],
    );
  }

  Widget _buildTabItem(String tabType, String label, String icon) {
    bool isSelected = currentSelectedTab == tabType;
    List<SuggestionItem> filteredItems = widget.taggedItems
        .where((item) => item.type == tabType)
        .toList();

    return GestureDetector(
      onTap: () {
        // Call the onTabTapped callback first (for API calls in post cards)
        if (widget.onTabTapped != null) {
          widget.onTabTapped!(tabType);
        }

        setState(() {
          // Toggle: if tapping the already selected tab, deselect it
          currentSelectedTab = isSelected ? '' : tabType;
        });
        if (widget.onTabChanged != null) {
          widget.onTabChanged!(isSelected ? '' : tabType);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.brandBlack : AppColors.textFieldFill2,
          borderRadius: BorderRadius.circular(20),
          // border: Border.all(
          //   color: isSelected ? AppColors.brandBlack : AppColors.brandBlack.withOpacity(0.1),
          //   width: 1,
          // ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              width: 16,
              height: 16,
              color: isSelected ? AppColors.appWhite : AppColors.appBlack,
            ),
            if (filteredItems.isNotEmpty) ...[
              const SizedBox(width: 4),
              Text(
                '${filteredItems.length} ',
                style: AppTextStyle.contentText0(
                  textColor: isSelected ? AppColors.appWhite : AppColors.appBlack,
                ).copyWith(fontSize: 12, fontWeight: FontWeight.w500),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleItemTap(SuggestionItem item) {
    if (item.reference == null || item.reference!.isEmpty) return;

    if (!context.mounted) return;

    switch (item.type?.toUpperCase()) {
      case 'PRODUCT':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuyerViewSingleProductScreen(
              productReference: item.reference!,
            ),
          ),
        );
        break;
      case 'USER':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(
              userReference: item.reference!,
            ),
          ),
        );
        break;
      case 'STORE':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuyerViewStoreScreen(
              storeReference: item.reference!,
              isStoreOwnerView: AppConstants.appData.storeReference == item.reference,
            ),
          ),
        );
        break;
    }
  }

  Widget _buildTaggedItemsList() {
    // Don't show anything if no tab is selected
    if (currentSelectedTab.isEmpty) {
      return const SizedBox.shrink();
    }

    List<SuggestionItem> filteredItems = getFilteredItems();

    // Only show the empty state if a tab is selected but has no items
    if (filteredItems.isEmpty) {
      return Container(
        height: 80,
        alignment: Alignment.center,
        child: Text(
          'No ${currentSelectedTab.toLowerCase()}s tagged',
          style: AppTextStyle.contentText0(
            textColor: AppColors.disableBlack,
          ),
        ),
      );
    }

    return SizedBox(
      height: 200, // Extra space for better spacing
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filteredItems.length,
        itemBuilder: (context, index) {
          final item = filteredItems[index];
          return GestureDetector(
            onTap: () => _handleItemTap(item),
            child: Container(
              width: 125,
              margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: AppColors.borderColor1,
                  width: 1.0,
                ),
              ),
              child: Stack(
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Image section
                      SizedBox(
                        height: 125,
                        child: ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(5),
                            topRight: Radius.circular(5),
                          ),
                          child: extendedImage(
                            item.imageUrl,
                            context,
                            125,
                            125,
                            customPlaceHolder: item.type?.toUpperCase() == 'PRODUCT'
                                ? AppImages.productPlaceHolder
                                : item.type?.toUpperCase() == 'STORE'
                                    ? AppImages.storePlaceHolder
                                    : AppImages.userPlaceHolder,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      // Bottom text section
                      Container(
                        height: 50,
                        padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 2),
                        decoration: const BoxDecoration(
                          color: AppColors.appWhite,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(5),
                            bottomRight: Radius.circular(5),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.type?.toUpperCase() == 'PRODUCT' && item.secondaryText?.isNotEmpty == true) ...[
                              Text(
                                item.secondaryText ?? '',
                                style: AppTextStyle.smallTextRegular(
                                  textColor: AppColors.appBlack,
                                ).copyWith(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '@${item.primaryText ?? ''}',
                                style: AppTextStyle.smallTextRegular(
                                  textColor: AppColors.appBlack,
                                ).copyWith(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ] else ...[
                              if (item.secondaryText?.isNotEmpty == true) ...[
                                Text(
                                  item.secondaryText ?? '',
                                  style: AppTextStyle.smallTextRegular(
                                    textColor: AppColors.appBlack,
                                  ).copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                              ],
                              Text(
                                '@${item.primaryText ?? ''}',
                                style: AppTextStyle.smallTextRegular(
                                  textColor: AppColors.appBlack,
                                ).copyWith(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ]
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (widget.showRemoveButton && widget.onRemoveItem != null)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () {
                          widget.onRemoveItem!(index);
                        },
                        child: Container(
                          width: 16,
                          height: 16,
                          alignment: Alignment.center,
                          decoration: const BoxDecoration(
                            color: AppColors.appBlack,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 12,
                            color: AppColors.appWhite,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },

      ),
    );
  }
}
