import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/post/add_post/add_post_bloc.dart';
import 'package:swadesic/features/post/add_post/widgets/typing_suggestions_overlay.dart';
import 'package:swadesic/features/post/edit_post/edit_post_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/widgets/tagged_items_widget/tagged_items_widget.dart';

class EditPostScreen extends StatefulWidget {
  final PostDetail postDetail;
  final bool? isCommentEdit;

  const EditPostScreen(
      {super.key, required this.postDetail, this.isCommentEdit});

  @override
  State<EditPostScreen> createState() => _EditPostScreenState();
}

class _EditPostScreenState extends State<EditPostScreen> {
  //Bloc
  late EditPostBloc editPostBloc;

  //region Init
  @override
  void initState() {
    editPostBloc = EditPostBloc(context, widget.postDetail);
    editPostBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    editPostBloc.dispose();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: StreamBuilder<EditPostScreenState>(
          stream: editPostBloc.editUpdatePostScreenStateCtrl.stream,
          builder: (context, snapshot) {
            return Stack(
              children: [
                Scaffold(
                  floatingActionButton: postButton(),
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.endFloat,
                  appBar: appBar(),
                  body: SafeArea(child: body()),
                ),
                Visibility(
                  visible: snapshot.data == EditPostScreenState.Loading,
                  child: Positioned.fill(
                    child: Container(
                      color: AppColors.textFieldFill1.withOpacity(0.8),
                      alignment: Alignment.center,
                      child: AppCommonWidgets.appCircularProgress(),
                    ),
                  ),
                )
              ],
            );
          }),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
      },
      context: context,
      isCustomTitle: false,
      title: AppStrings.edit,
      isMembershipVisible: false,
      isCustomMenuVisible: true,
      isDefaultMenuVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          userInfo(),
          postImages(),
          writeYourThough(),
          taggedItemsPreview(),
          access(),
        ],
      ),
    );
  }

//endregion

  //region Widget user info

  Widget userInfo() {
    final loggedInUser = Provider.of<LoggedInUserInfoDataModel>(
        context); // Access the provided data
    final loggedInStore = Provider.of<SellerOwnStoreInfoDataModel>(
        context); // Access the provided data
    // final value = context.watch<LoggedInUserInfoDataModel?>();
    // final value = context.watch<SellerOwnStoreInfoDataModel?>();

    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(
        bottom: 5,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(100),
            child: SizedBox(
              height: 27,
              width: 27,
              child: extendedImage(
                  AppConstants.appData.isUserView!
                      ? loggedInUser.userDetail!.icon
                      : loggedInStore.storeInfo!.icon,
                  context,
                  100,
                  100,
                  customPlaceHolder: AppImages.userPlaceHolder),
            ),
          ),
          const SizedBox(
            width: 5,
          ),
          Text(
            AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.userName!
                : loggedInStore.storeInfo!.storehandle!,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
                .copyWith(height: 0),
          ),
          VerifiedBadge(
            width: 15,
            height: 15,
            subscriptionType: AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.subscriptionType
                : loggedInStore.storeInfo!.subscriptionType,
          ),
        ],
      ),
    );
  }

//endregion

  //region Post images
  Widget postImages() {
    return StreamBuilder<bool>(
        stream: editPostBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          //If not empty
          if (editPostBloc.postDetail.images!.isNotEmpty ||
              editPostBloc.selectedImage.isNotEmpty) {
            return Container(
              alignment: Alignment.centerLeft,
              height: 236,
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: widget.postDetail.images!.length +
                    editPostBloc.selectedImage.length,
                itemBuilder: (context, index) {
                  if (index < widget.postDetail.images!.length) {
                    // Network image
                    return Stack(
                      children: [
                        PostAndProductImageWidgets(
                          localOrNetworkImage:
                              widget.postDetail.images![index].mediaPath!,
                        ),
                        Positioned(
                          right: 15,
                          top: 5,
                          child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                //If from post
                                if (widget.postDetail.postOrCommentReference!
                                    .startsWith("P")) {
                                  editPostBloc.deletePostImage(
                                      imageId: widget
                                          .postDetail.images![index].mediaId!,
                                      postReference: widget
                                          .postDetail.postOrCommentReference!);
                                } else {
                                  editPostBloc.deleteCommentImage(
                                      imageId: widget
                                          .postDetail.images![index].mediaId!,
                                      postOrCommentReference: widget
                                          .postDetail.postOrCommentReference!);
                                }
                              },
                              child: Opacity(
                                opacity: 0.5,
                                child:
                                    SvgPicture.asset(AppImages.removeCircle1),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  } else {
                    // Local image
                    int localIndex = index - widget.postDetail.images!.length;
                    File localImagePath =
                        editPostBloc.selectedImage[localIndex];
                    return Stack(
                      children: [
                        PostAndProductImageWidgets(
                          localOrNetworkImage: localImagePath.path,
                        ),
                        Positioned(
                          right: 15,
                          top: 5,
                          child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                editPostBloc.removeLocalImage(
                                    filePath: localImagePath.path);
                              },
                              child: Opacity(
                                opacity: 0.5,
                                child:
                                    SvgPicture.asset(AppImages.removeCircle1),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                },
              ),
            );
          }

          return const SizedBox();
        });
  }

  //endregion

//region Write your though
  Widget writeYourThough() {
    return Column(
      children: [
        //Text field
        TextFormField(
            autofocus: true,
            maxLines: 10,
            minLines: 3,
            textCapitalization: TextCapitalization.sentences,
            inputFormatters: [
              LengthLimitingTextInputFormatter(500),
            ],
            textAlign: TextAlign.start,
            textInputAction: TextInputAction.none,
            keyboardType: TextInputType.multiline,
            controller: editPostBloc.addPostTextCtrl,
            scrollPadding: EdgeInsets.only(
                bottom: MediaQuery.of(context).size.height * 0.2),
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            decoration: InputDecoration(
              isDense: true,
              hintStyle:
                  AppTextStyle.hintText(textColor: AppColors.writingBlack1),
              fillColor: AppColors.appWhite,
              // Specify the desired internal color
              filled: true,
              hintText: "write your thoughts..",
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
              border: InputBorder.none, // Remove all borders
            )),
        //Rating
        StreamBuilder<bool>(
            stream: editPostBloc.refreshCtrl.stream,
            builder: (context, snapshot) {
              return Visibility(
                visible:
                    widget.postDetail.commentType == CommentEnums.REVIEW.name,
                child: RatingBar.builder(
                  initialRating:
                      widget.postDetail.commentType == CommentEnums.REVIEW.name
                          ? double.parse(editPostBloc.reviewCount)
                          : 5.0,
                  minRating: 1,
                  direction: Axis.horizontal,
                  glowColor: AppColors.yellow,
                  unratedColor: AppColors.lightGray,
                  allowHalfRating: false,
                  itemCount: 5,
                  itemPadding: const EdgeInsets.symmetric(horizontal: 5.0),
                  itemBuilder: (context, _) => const Icon(
                    Icons.star,
                    color: Colors.amber,
                  ),
                  onRatingUpdate: (rating) {
                    editPostBloc.reviewCount = rating.toString();
                    // widget.singlePostViewBloc.commentFieldsBloc.ratingCount = rating.round().toString();
                    //buyerProductCommentBloc.productRating = rating.round();
                    // //print(buyerProductCommentBloc.productRating);
                  },
                ),
              );
            }),
        // Typing suggestions overlay
        StreamBuilder<bool>(
          stream: editPostBloc.showSuggestionsCtrl.stream,
          initialData: false,
          builder: (context, showSnapshot) {
            if (!showSnapshot.data!) return const SizedBox.shrink();

            return StreamBuilder<List<SuggestionItem>>(
              stream: editPostBloc.suggestionsCtrl.stream,
              initialData: const [],
              builder: (context, suggestionsSnapshot) {
                return StreamBuilder<bool>(
                  stream: editPostBloc.suggestionsLoadingCtrl.stream,
                  initialData: false,
                  builder: (context, loadingSnapshot) {
                    return TypingSuggestionsOverlay(
                      suggestions: suggestionsSnapshot.data ?? [],
                      onSuggestionTap: editPostBloc.onSuggestionTap,
                      isLoading: loadingSnapshot.data ?? false,
                      onLoadMore: editPostBloc.loadMoreSuggestions,
                      hasMore: editPostBloc.hasMoreSuggestions,
                    );
                  },
                );
              },
            );
          },
        ),
      ],
    );
  }

//endregion

  //region Tagged items preview
  Widget taggedItemsPreview() {
    return StreamBuilder<List<SuggestionItem>>(
      stream: editPostBloc.taggedItemsFromAccessCtrl.stream,
      initialData: const [],
      builder: (context, snapshot) {
        if (snapshot.data == null || snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: StreamBuilder<String>(
            stream: editPostBloc.selectedTagTabCtrl.stream,
            initialData: 'PRODUCT',
            builder: (context, tabSnapshot) {
              return TaggedItemsWidget(
                taggedItems: snapshot.data!,
                showRemoveButton: true,
                selectedTab: tabSnapshot.data ?? 'PRODUCT',
                onTabChanged: (tab) => editPostBloc.selectTagTab(tab),
                onRemoveItem: (index) => editPostBloc.removeTaggedItem(index),
                showTabs: true,
              );
            },
          ),
        );
      },
    );
  }
  //endregion

//region Access
  Widget access() {
    // Check if we're editing a comment (reference starts with "C")
    bool isEditingComment =
        widget.postDetail.postOrCommentReference!.startsWith("C");

    return Column(
      children: [
        //Add photos
        Consumer<AppConfigDataModel>(
          builder:
              (BuildContext context, AppConfigDataModel value, Widget? child) {
            return AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionText:
                  "${AppStrings.addPhotos} (up to ${value.appConfig!.postImageLimit})",
              onTap: () {
                editPostBloc.onTapAddImage();
              },
            );
          },
        ),

        // Show additional options only when editing posts, not comments
        if (!isEditingComment) ...[
          Consumer<AppConfigDataModel>(
            builder:
                (BuildContext context, AppConfigDataModel value, Widget? child) {
              return AppCommonWidgets.settingOption(
                horizontalPadding: 15,
                optionText:
                  AppStrings.tagStoreProductMember,
                onTap: () {
                  editPostBloc.onTapTagStoreProductMember();
                },
              );
            },
          ),
          AppToolTip(
            message: AppStrings.thisFeatureIsCommingSoon,
            toolTipWidget: IgnorePointer(
              child: AppCommonWidgets.settingOption(
                horizontalPadding: 15,
                optionTextColor: AppColors.disableBlack,
              optionText: AppStrings.addGifVideo,
              arrowColor: AppColors.disableBlack,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.addLocation,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.tagStorePeople,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.promoteProduct,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        ],
      ],
    );
  }

//endregion

//region Post button
  Widget postButton() {
    return FloatingActionButton.extended(
        backgroundColor: AppColors.appWhite,
        extendedPadding: EdgeInsets.zero,
        isExtended: true,
        elevation: 0,
        tooltip: AppStrings.postButton,
        label: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            ///Post or comment
            Row(
              children: [
                CupertinoButton(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(100),
                  onPressed: () {
                    //If from post
                    if (widget.postDetail.postOrCommentReference!
                        .startsWith("P")) {
                      editPostBloc.editPostApiCall();
                    } else {
                      editPostBloc.editCommentApiCall();
                    }
                  },
                  padding:
                      const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                  child: Text(
                    widget.postDetail.postOrCommentReference!.startsWith("C")
                        ? AppStrings.sendButton
                        : AppStrings.postButton,
                    style: AppTextStyle.access0(textColor: AppColors.appWhite),
                  ),
                ),
              ],
            ),
          ],
        ),
        onPressed: null);
  }
//endregion
}

// CupertinoButton(
// color: AppColors.brandGreen,
// borderRadius: BorderRadius.circular(100),
// onPressed: () {
// // sellerStoreDeliverySettingBloc.onTapSave();
// },
// padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
// child: Text(
// AppStrings.saveChanges,
// style: AppTextStyle.access0(textColor: AppColors.appWhite),
// ),
// ),
